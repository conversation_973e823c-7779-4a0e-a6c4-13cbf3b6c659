const bacnetService = require("../../services/bacnet/bacnet.service");

module.exports = {
  friendlyName: "Fetch BACnet Devices",
  description:
    "Fetch BACnet devices for a given BACnet slave controller with their location and configuration status",
  example: [
    'curl -X GET "http://**************:1337/m2/site/sjo-del/bacnet/36819/devices" -H "Authorization: Bearer token"',
  ],

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
      description: "User meta information added by default to authenticated routes",
    },
    siteId: {
      type: "string",
      required: true,
      example: "gknmh",
      description: "Site ID",
    },
    bacnetSlaveControllerId: {
      type: "string",
      required: true,
      example: "36819",
      description:
        "BACnet slave controller ID (deviceId of the virtual slave controller in DynamoDB)",
    },
  },

  exits: {
    success: {
      responseType: "ok",
      statusCode: 200,
    },
    badRequest: {
      responseType: "badRequest",
      statusCode: 400,
    },
    serverError: {
      responseType: "serverError",
      statusCode: 500,
    },
    notFound: {
      responseType: "notFound",
      statusCode: 404,
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { siteId, bacnetSlaveControllerId } = inputs;

      const slaveController = await Devices.findOne({
        deviceId: bacnetSlaveControllerId,
        siteId: siteId,
      });

      if (!slaveController || !slaveController.isSlaveController) {
        return exits.notFound({
          message: "BACnet slave controller not found for the given site",
        });
      }

      const bacnetDevices = await bacnetService.fetchBacnetDevices(siteId, bacnetSlaveControllerId);

      return exits.success(bacnetDevices);
    } catch (error) {
      sails.log.error("[BACnet > fetch-bacnet-devices] Error:", error);
      return exits.serverError({
        message: "Failed to fetch BACnet devices",
        error: error.message,
      });
    }
  },
};
