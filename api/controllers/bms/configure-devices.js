const bmsService = require('../../services/bms/bms.service');

/**
 * Configure discovered devices - handles both newly discovered devices and existing third-party master controller location updates
 *
 * Newly discovered devices = Discovered devices that will be configured as third-party master controllers
 * Existing devices = Third-party master controllers that need location updates
 */
module.exports = {
  friendlyName: 'Configure Discovered Devices',
  description: 'Configure newly discovered devices as third-party master controllers and update existing device locations',

  inputs: {
    _userMeta: {
      type: 'json',
      required: true,
      example: { id: 'userId', _role: 'role', _site: 'siteId' },
      description: 'User meta information added by default to authenticated routes'
    },

    siteId: {
      type: 'string',
      required: true,
      description: 'Site ID where devices should be configured'
    },

    slaveControllerId: {
      type: 'string',
      required: true,
      description: 'Slave controller ID (deviceId in DynamoDB)'
    },

    devices: {
      type: 'ref',
      required: true,
      description: 'Array of discovered devices to configure with their location assignments',
      example: [
        {
          discoveredDeviceId: "1",
          controllerId: null, // null for new devices, deviceId for existing
          name: "AHU-01", // device name from deviceProperties
          location: {
            areaId: "roof-area",
            regionId: "hvac-region",
            leafNodeId: null // for IBMS sites
          }
        }
      ]
    }
  },

  exits: {
    success: {
      responseType: "ok",
      statusCode: 200,
    },
    badRequest: {
      responseType: "badRequest",
      statusCode: 400,
    },
    notFound: {
      responseType: "notFound",
      statusCode: 404,
    },
    serverError: {
      responseType: "serverError",
      statusCode: 500,
    },
  },

  fn: async function (inputs, exits) {
    const { siteId, slaveControllerId, devices } = inputs;

    try {
      // Validate inputs
      if (!devices || !Array.isArray(devices) || devices.length === 0) {
        throw new Error('Devices array is required and must not be empty');
      }

      const results = await bmsService.configureDiscoveredDevices(siteId, slaveControllerId, devices);

      // Calculate total DeJoule devices updated
      const totalDeJouleDevicesUpdated = results.existingDevicesUpdated.reduce((total, device) => {
        return total + (device.dejouleDevicesUpdated || 0);
      }, 0);

      const response = {
        message: `Successfully configured ${devices.length} discovered devices`,
        summary: {
          total: devices.length,
          thirdPartyControllersCreated: results.newDevicesCreated.length,
          thirdPartyControllersUpdated: results.existingDevicesUpdated.length,
          dejouleDevicesUpdated: totalDeJouleDevicesUpdated,
          errors: results.errors.length
        },
        results: {
          thirdPartyControllersCreated: results.newDevicesCreated,
          thirdPartyControllersUpdated: results.existingDevicesUpdated,
          errors: results.errors
        },
        errors: results.errors.length > 0 ? results.errors : undefined
      };

      sails.log.info(`Discovered device configuration completed: ${results.newDevicesCreated.length} third-party controllers created, ${results.existingDevicesUpdated.length} third-party controllers updated, ${totalDeJouleDevicesUpdated} DeJoule devices updated, ${results.errors.length} errors`);

      return exits.success(response);

    } catch (error) {
      sails.log.error('[BMS > configure-devices] Error:', error);

      if (error.code === 'E_NOT_FOUND') {
        return exits.notFound({ message: error.message });
      }

      if (error.code === 'E_BAD_REQUEST') {
        return exits.badRequest({ message: error.message });
      }

      return exits.serverError({
        message: 'Failed to configure discovered devices',
        error: error.message
      });
    }
  }
};
