const bmsService = require("../../services/bms/bms.service");

module.exports = {
  friendlyName: "Fetch Discovered Devices",
  description:
    "<PERSON><PERSON> discovered devices for a given slave controller with their location and configuration status",
  example: [
    'curl -X GET "http://**************:1337/m2/site/sjo-del/bms/36819/devices" -H "Authorization: Bearer token"',
  ],

  inputs: {
    _userMeta: {
      type: "json",
      required: true,
      example: { id: "userId", _role: "role", _site: "siteId" },
      description: "User meta information added by default to authenticated routes",
    },
    siteId: {
      type: "string",
      required: true,
      example: "gknmh",
      description: "Site ID",
    },
    slaveControllerId: {
      type: "string",
      required: true,
      example: "36819",
      description:
        "Slave controller ID (deviceId of the virtual slave controller in DynamoDB)",
    },
  },

  exits: {
    success: {
      responseType: "ok",
      statusCode: 200,
    },
    badRequest: {
      responseType: "badRequest",
      statusCode: 400,
    },
    serverError: {
      responseType: "serverError",
      statusCode: 500,
    },
    notFound: {
      responseType: "notFound",
      statusCode: 404,
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { siteId, slaveControllerId } = inputs;

      const slaveController = await Devices.findOne({
        deviceId: slaveControllerId,
        siteId: siteId,
      });

      if (!slaveController || !slaveController.isSlaveController) {
        return exits.notFound({
          message: "Slave controller not found for the given site",
        });
      }

      const discoveredDevices = await bmsService.fetchDiscoveredDevices(siteId, slaveControllerId);

      return exits.success(discoveredDevices);
    } catch (error) {
      sails.log.error("[BMS > fetch-discovered-devices] Error:", error);
      return exits.serverError({
        message: "Failed to fetch discovered devices",
        error: error.message,
      });
    }
  },
};
