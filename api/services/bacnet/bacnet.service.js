const moment = require('moment-timezone');
const flaverr = require('flaverr');
const siteService = require("../site/site.service");

module.exports = {
  /**
   * Fetch BACnet devices for a given BACnet slave controller
   * @param {string} siteId - Site ID
   * @param {string} bacnetSlaveControllerId - BACnet slave controller ID (deviceId in DynamoDB)
   * @returns {Array} Array of BACnet devices with location and configuration status
   */
  fetchBacnetDevices: async function (siteId, bacnetSlaveControllerId) {
    try {
      // Step 1: Check if site is IBMS to determine location structure
      const siteInfo = await siteService.findOne({ siteId, status: 1 });
      const isIBMS = siteInfo && siteInfo.industryType && siteInfo.industryType.includes("ibms");

      // Step 2: Get BACnet connector
      const connectors = await BMSConnector.find({
        slaveControllerId: parseInt(bacnetSlaveControllerId),
        siteId: siteId,
        status: 1,
      });

      if (!connectors || connectors.length === 0) {
        sails.log.warn(
          `No BACnet connector found for slave controller ${bacnetSlaveControllerId} in site ${siteId}`
        );
        return [];
      }

      const connector = connectors[0];
      const connectorId = connector.id;

      // Step 3: Get all BACnet devices
      const bacnetDevices = await BMSDevice.find({
        bmsConnectorRefId: connectorId,
      }).sort("id ASC");

      if (!bacnetDevices || bacnetDevices.length === 0) {
        sails.log.info(`No BACnet devices found for connector ${connectorId}`);
        return [];
      }

      // Step 4: Get device objects for each BACnet device
      const deviceIds = bacnetDevices.map((device) => device.id);
      const deviceObjects = await BMSDeviceObject.find({
        deviceRefId: { in: deviceIds },
      }).sort(["deviceRefId ASC", "id ASC"]);

      // Step 5: Get properties for all objects
      const objectIds = deviceObjects.map((obj) => obj.id);
      let objectProperties = [];

      if (objectIds.length > 0) {
        objectProperties = await BMSObjectProperty.find({
          objectRefId: { in: objectIds },
        }).sort(["objectRefId ASC", "id ASC"]);
      }

      // Step 6: Get configured devices using ref_device_id from BACnet devices
      const configuredDevices = await this.getConfiguredBacnetDevices(
        siteId,
        bacnetDevices,
        isIBMS
      );

      // Step 7: Build the response format
      const response = await this.buildBacnetDeviceResponse(
        bacnetDevices,
        deviceObjects,
        objectProperties,
        configuredDevices,
        isIBMS
      );

      return response;
    } catch (error) {
      sails.log.error("[BACnet Service] Error fetching BACnet devices:", error);
      throw error;
    }
  },

  /**
   * Get configured BACnet devices using ref_device_id from BMSDevice table
   * @param {string} siteId - Site ID
   * @param {Array} bacnetDevices - BACnet devices from PostgreSQL
   * @param {boolean} isIBMS - Whether the site is IBMS
   * @returns {Object} Map of BACnet device ID to DynamoDB device info
   */
  getConfiguredBacnetDevices: async function (siteId, bacnetDevices, isIBMS) {
    try {
      const configuredDevicesMap = {};

      for (const bacnetDevice of bacnetDevices) {
        if (bacnetDevice.refDeviceId) {
          try {
            // Get the DeJoule device (third-party master controller) from DynamoDB
            const thirdPartyMasterController = await Devices.findOne({
              deviceId: bacnetDevice.refDeviceId.toString(),
              siteId: siteId,
            });

            if (thirdPartyMasterController) {
              let locationInfo = {
                deviceId: thirdPartyMasterController.deviceId,
              };

              if (isIBMS) {
                // For IBMS sites, get leafNodeId from the hierarchy
                locationInfo.leafNodeId = await this.getLeafNodeIdForDevice(
                  thirdPartyMasterController.deviceId,
                  siteId
                );
              } else {
                // For non-IBMS sites, use areaId and regionId
                locationInfo.areaId = thirdPartyMasterController.areaId;
                locationInfo.regionId = thirdPartyMasterController.regionId;
              }

              configuredDevicesMap[bacnetDevice.id.toString()] = locationInfo;
            }
          } catch (deviceError) {
            sails.log.warn(
              `[BACnet Service] Could not find third-party master controller ${bacnetDevice.refDeviceId} for BACnet device ${bacnetDevice.id}:`,
              deviceError
            );
            // Continue processing other devices
          }
        }
      }

      return configuredDevicesMap;
    } catch (error) {
      sails.log.error(
        "[BACnet Service] Error getting configured devices via ref_device_id:",
        error
      );
      throw error;
    }
  },

  /**
   * Get leaf node ID for IBMS device from hierarchy
   * @param {string} deviceId - Device ID
   * @param {string} siteId - Site ID
   * @returns {string|null} Leaf node ID or null
   */
  getLeafNodeIdForDevice: async function (deviceId, siteId) {
    try {
      // Query the nodes table to find the leaf node for this device
      const query = `
        SELECT id as leaf_node_id
        FROM nodes
        WHERE device_id = $1 AND site_id = $2 AND level_type = 'component' AND is_deleted = false
        LIMIT 1
      `;

      const result = await sails
        .getDatastore("postgres")
        .sendNativeQuery(query, [deviceId, siteId]);

      return result.rows && result.rows.length > 0 ? result.rows[0].leaf_node_id.toString() : null;
    } catch (error) {
      sails.log.error("[BACnet Service] Error getting leaf node ID:", error);
      return null;
    }
  },

  /**
   * Build the response format for BACnet devices
   * @param {Array} bacnetDevices - BACnet devices from PostgreSQL
   * @param {Array} deviceObjects - Device objects from PostgreSQL
   * @param {Array} objectProperties - Object properties from PostgreSQL
   * @param {Object} configuredDevices - Configured devices from DynamoDB
   * @param {boolean} isIBMS - Whether the site is IBMS
   * @returns {Array} Formatted response array
   */
  buildBacnetDeviceResponse: async function (
    bacnetDevices,
    deviceObjects,
    objectProperties,
    configuredDevices,
    isIBMS
  ) {
    try {
      // Group objects by device ID
      const objectsByDevice = {};
      deviceObjects.forEach((obj) => {
        if (!objectsByDevice[obj.deviceRefId]) {
          objectsByDevice[obj.deviceRefId] = [];
        }
        objectsByDevice[obj.deviceRefId].push(obj);
      });

      // Group properties by object ID
      const propertiesByObject = {};
      objectProperties.forEach((prop) => {
        if (!propertiesByObject[prop.objectRefId]) {
          propertiesByObject[prop.objectRefId] = [];
        }
        propertiesByObject[prop.objectRefId].push(prop);
      });

      // Build response
      const response = bacnetDevices.map((device) => {
        const bacnetDeviceId = device.id.toString();
        const configuredDevice = configuredDevices[bacnetDeviceId];

        // Build BACnet properties object
        const bacnetProperties = {
          name: device.name,
          address: device.address,
          vendorName: device.vendorName,
          modelName: device.modelName,
          systemStatus: device.systemStatus,
          description: device.description,
          location: device.location,
        };

        // Add device objects and their properties
        const objects = objectsByDevice[device.id] || [];
        objects.forEach((obj) => {
          const properties = propertiesByObject[obj.id] || [];
          properties.forEach((prop) => {
            const key = `${obj.name || obj.address}_${prop.propertyTag}`;
            bacnetProperties[key] = prop.recentValue || prop.address;
          });
        });

        // Build location object based on site type
        let location = null;
        if (configuredDevice) {
          if (isIBMS) {
            location = {
              areaId: null,
              regionId: null,
              leafNodeId: configuredDevice.leafNodeId,
            };
          } else {
            location = {
              areaId: configuredDevice.areaId,
              regionId: configuredDevice.regionId,
              leafNodeId: null,
            };
          }
        }

        return {
          bacnetDeviceId: bacnetDeviceId,
          controllerId: configuredDevice ? configuredDevice.deviceId : null,
          location: location,
          bacnetProperties: bacnetProperties,
        };
      });

      return response;
    } catch (error) {
      sails.log.error("[BACnet Service] Error building response:", error);
      throw error;
    }
  },

  /**
   * Get unconfigured BACnet devices (devices without refDeviceId) for discovery save
   * @param {string} siteId - Site ID
   * @param {string} bacnetSlaveControllerId - BACnet slave controller ID
   * @returns {Array} Array of unconfigured BACnet devices ready for save API
   */
  getUnconfiguredBacnetDevices: async function (siteId, bacnetSlaveControllerId) {
    try {
      // Get all BACnet devices (both configured and unconfigured)
      const allDevices = await this.fetchBacnetDevices(siteId, bacnetSlaveControllerId);

      // Filter only unconfigured devices (no controllerId means not saved as controller yet)
      const unconfiguredDevices = allDevices.filter(device => !device.controllerId);

      sails.log.info(`Found ${unconfiguredDevices.length} unconfigured BACnet devices for site ${siteId}`);

      return unconfiguredDevices;
    } catch (error) {
      sails.log.error("[BACnet Service] Error getting unconfigured devices:", error);
      throw error;
    }
  },

  /**
   * Configure BACnet devices - handles both newly discovered BACnet devices and existing third-party master controller location updates
   * @param {string} siteId - Site ID
   * @param {string} bacnetSlaveControllerId - BACnet slave controller ID
   * @param {Array} devices - Array of devices to configure
   * @returns {Object} Configuration results
   */
  configureBacnetDevices: async function (siteId, bacnetSlaveControllerId, devices) {
    // Validate site exists
    const site = await Sites.findOne({ id: siteId });
    if (!site) {
      throw flaverr('E_NOT_FOUND', new Error('Site not found'));
    }

    // Check if site is IBMS to determine location structure
    const isIBMS = site.industryType && site.industryType.includes("ibms");

    // Validate BACnet slave controller exists
    const slaveController = await Devices.findOne({
      deviceId: bacnetSlaveControllerId,
      siteId: siteId,
    });

    if (!slaveController || !slaveController.isSlaveController) {
      throw flaverr('E_NOT_FOUND', new Error('BACnet slave controller not found'));
    }

    // Separate new BACnet devices from existing third-party master controllers
    const newBacnetDevices = devices.filter(device => !device.controllerId);
    const existingControllers = devices.filter(device => device.controllerId);

    const results = {
      newDevicesCreated: [],
      existingDevicesUpdated: [],
      errors: []
    };

    // Process new BACnet devices (create as third-party master controllers)
    if (newBacnetDevices.length > 0) {
      const newDeviceResults = await this.processNewBacnetDevices(newBacnetDevices, site, slaveController, isIBMS);
      results.newDevicesCreated = newDeviceResults.created;
      results.errors.push(...newDeviceResults.errors);
    }

    // Process existing third-party master controllers (location updates)
    if (existingControllers.length > 0) {
      const existingDeviceResults = await this.processExistingBacnetDevices(existingControllers, site, isIBMS);
      results.existingDevicesUpdated = existingDeviceResults.updated;
      results.errors.push(...existingDeviceResults.errors);
    }

    return results;
  },

  /**
   * Process new BACnet devices - create them as third-party master controllers
   * @param {Array} newBacnetDevices - Array of new BACnet devices to create as third-party master controllers
   * @param {Object} site - Site object
   * @param {Object} slaveController - BACnet slave controller
   * @param {boolean} isIBMS - Whether site is IBMS
   * @returns {Object} Results with created devices and errors
   */
  processNewBacnetDevices: async function (newBacnetDevices, site, slaveController, isIBMS) {
    const created = [];
    const errors = [];

    // Get next device ID from DyanmoKeyStore
    const deviceIdCount = await DyanmoKeyStore.findOne({ key: 'totalDeviceCount' });
    let nextDeviceId = deviceIdCount ? parseInt(deviceIdCount.value) + 1 : 1;

    for (const deviceData of newBacnetDevices) {
      try {
        const { bacnetDeviceId, location, name } = deviceData;

        // Validate required fields
        if (!bacnetDeviceId || !name) {
          throw flaverr('E_BAD_REQUEST', new Error(`Device missing required fields: bacnetDeviceId and name are required`));
        }

        // Use the discovered BACnet device name directly
        const controllerName = name.trim();

        // Determine location based on site type and provided data
        let locationConfig = {};
        if (isIBMS) {
          locationConfig.regionId = location?.regionId || 'ibms';
          locationConfig.areaId = location?.areaId || 'ibms';
          locationConfig.leafNodeId = location?.leafNodeId;
        } else {
          locationConfig.networkId = location?.networkId || Object.keys(site.networks)[0];
          locationConfig.regionId = location?.regionId || Object.keys(site.regions)[0];
          locationConfig.areaId = location?.areaId || Object.keys(site.areas || {})[0];
        }

        // Validate location exists in site (skip validation for IBMS special values)
        if (!isIBMS) {
          if (!site.networks || !site.networks[locationConfig.networkId]) {
            throw flaverr('E_BAD_REQUEST', new Error(`Network '${locationConfig.networkId}' does not exist in site`));
          }
          if (!site.regions || !site.regions[locationConfig.regionId]) {
            throw flaverr('E_BAD_REQUEST', new Error(`Region '${locationConfig.regionId}' does not exist in site`));
          }
          if (!site.areas || !site.areas[locationConfig.areaId]) {
            throw flaverr('E_BAD_REQUEST', new Error(`Area '${locationConfig.areaId}' does not exist in site`));
          }
        }

        // Create device configuration for discovered BACnet third-party master controller
        const deviceConfig = {
          deviceId: String(nextDeviceId),
          siteId: site.id,
          name: controllerName,
          deviceType: 'n3uronbacnetmqtt-controller',
          vendorId: 'n3uronbacnetmqtt',
          ...locationConfig,
          secondaryControllerId: parseInt(slaveController.deviceId),
          isSlaveController: 0,
        };

        const newDevice = await Devices.create(deviceConfig);

        // Update BMSDevice.refDeviceId to link to the new controller
        await BMSDevice.updateOne({
          id: parseInt(bacnetDeviceId)
        }).set({
          refDeviceId: parseInt(newDevice.deviceId)
        });

        // Update site networks and regions to include this controller (for non-IBMS sites)
        if (!isIBMS) {
          await this.updateSiteReferencesForNewDevice(site, deviceConfig, nextDeviceId);
        }

        // Update total device count in DyanmoKeyStore
        await DyanmoKeyStore.updateOne({ key: 'totalDeviceCount' }).set({ value: String(nextDeviceId) });

        created.push({
          deviceId: newDevice.deviceId,
          name: newDevice.name,
          bacnetDeviceId: bacnetDeviceId,
          status: 'created'
        });

        nextDeviceId++;

      } catch (deviceError) {
        errors.push({
          device: deviceData,
          error: deviceError.message
        });
      }
    }

    return { created, errors };
  },

  /**
   * Process existing third-party master controllers - update their locations
   * @param {Array} existingDevices - Array of existing third-party master controllers to update
   * @param {Object} site - Site object
   * @param {boolean} isIBMS - Whether site is IBMS
   * @returns {Object} Results with updated devices and errors
   */
  processExistingBacnetDevices: async function (existingDevices, site, isIBMS) {
    const updated = [];
    const errors = [];

    for (const deviceData of existingDevices) {
      try {
        const { controllerId, location, name } = deviceData;

        if (!controllerId) {
          throw flaverr('E_BAD_REQUEST', new Error('Controller ID is required for existing devices'));
        }

        // Find the existing third-party master controller
        const existingDevice = await Devices.findOne({
          deviceId: controllerId,
          siteId: site.id
        });

        if (!existingDevice) {
          errors.push({
            device: deviceData,
            error: `Device with ID '${controllerId}' not found`
          });
          continue;
        }

        // Prepare update data based on site type
        let updateData = {};
        let locationChanged = false;

        if (isIBMS) {
          if (location?.leafNodeId && location.leafNodeId !== existingDevice.leafNodeId) {
            updateData.leafNodeId = location.leafNodeId;
            locationChanged = true;
          }
        } else {
          if (location?.areaId && location.areaId !== existingDevice.areaId) {
            if (!site.areas || !site.areas[location.areaId]) {
              throw flaverr('E_BAD_REQUEST', new Error(`Area '${location.areaId}' does not exist in site`));
            }
            updateData.areaId = location.areaId;
            locationChanged = true;
          }

          if (location?.regionId && location.regionId !== existingDevice.regionId) {
            if (!site.regions || !site.regions[location.regionId]) {
              throw flaverr('E_BAD_REQUEST', new Error(`Region '${location.regionId}' does not exist in site`));
            }
            updateData.regionId = location.regionId;
            locationChanged = true;
          }

          if (location?.networkId && location.networkId !== existingDevice.networkId) {
            if (!site.networks || !site.networks[location.networkId]) {
              throw flaverr('E_BAD_REQUEST', new Error(`Network '${location.networkId}' does not exist in site`));
            }
            updateData.networkId = location.networkId;
            locationChanged = true;
          }
        }

        // Update device name if provided and different
        if (name && name !== existingDevice.name) {
          updateData.name = name;
        }

        // Only update if there are changes
        if (Object.keys(updateData).length === 0) {
          continue;
        }

        // Update the device
        const updatedDevice = await Devices.updateOne({
          deviceId: controllerId,
          siteId: site.id
        }).set(updateData);

        // Update site networks and regions if location changed (for non-IBMS sites)
        if (locationChanged && !isIBMS) {
          await this.updateSiteLocationReferences(site, existingDevice, updatedDevice);
        }

        updated.push({
          deviceId: updatedDevice.deviceId,
          name: updatedDevice.name,
          changes: updateData,
          status: 'updated'
        });

      } catch (deviceError) {
        errors.push({
          device: deviceData,
          error: deviceError.message
        });
      }
    }

    return { updated, errors };
  },

  /**
   * Update site references for new device
   * @param {Object} site - Site object
   * @param {Object} deviceConfig - Device configuration
   * @param {number} deviceId - Device ID
   */
  updateSiteReferencesForNewDevice: async function (site, deviceConfig, deviceId) {
    try {
      const updatedSite = { ...site };

      if (updatedSite.networks[deviceConfig.networkId]) {
        if (!updatedSite.networks[deviceConfig.networkId].includes(String(deviceId))) {
          updatedSite.networks[deviceConfig.networkId].push(String(deviceId));
        }
      }

      if (updatedSite.regions[deviceConfig.regionId] && updatedSite.regions[deviceConfig.regionId].controller) {
        if (!updatedSite.regions[deviceConfig.regionId].controller.includes(String(deviceId))) {
          updatedSite.regions[deviceConfig.regionId].controller.push(String(deviceId));
        }
      }

      // Update site with new controller references
      await Sites.updateOne({ id: site.id }).set({
        networks: updatedSite.networks,
        regions: updatedSite.regions
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Update site location references when device location changes
   * @param {Object} site - Site object
   * @param {Object} oldDevice - Old device data
   * @param {Object} newDevice - Updated device data
   */
  updateSiteLocationReferences: async function (site, oldDevice, newDevice) {
    try {
      const updatedSite = { ...site };
      const deviceId = newDevice.deviceId;

      // Remove device from old network if network changed
      if (oldDevice.networkId !== newDevice.networkId) {
        if (oldDevice.networkId && updatedSite.networks[oldDevice.networkId]) {
          updatedSite.networks[oldDevice.networkId] = updatedSite.networks[oldDevice.networkId].filter(id => id !== deviceId);
        }

        // Add device to new network
        if (newDevice.networkId && updatedSite.networks[newDevice.networkId]) {
          if (!updatedSite.networks[newDevice.networkId].includes(deviceId)) {
            updatedSite.networks[newDevice.networkId].push(deviceId);
          }
        }
      }

      // Remove device from old region if region changed
      if (oldDevice.regionId !== newDevice.regionId) {
        if (oldDevice.regionId && updatedSite.regions[oldDevice.regionId] && updatedSite.regions[oldDevice.regionId].controller) {
          updatedSite.regions[oldDevice.regionId].controller = updatedSite.regions[oldDevice.regionId].controller.filter(id => id !== deviceId);
        }

        // Add device to new region
        if (newDevice.regionId && updatedSite.regions[newDevice.regionId] && updatedSite.regions[newDevice.regionId].controller) {
          if (!updatedSite.regions[newDevice.regionId].controller.includes(deviceId)) {
            updatedSite.regions[newDevice.regionId].controller.push(deviceId);
          }
        }
      }

      // Update site with new references
      await Sites.updateOne({ id: site.id }).set({
        networks: updatedSite.networks,
        regions: updatedSite.regions
      });
    } catch (error) {
      throw error;
    }
  },
};
